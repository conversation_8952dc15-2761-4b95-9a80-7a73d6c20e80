#!/usr/bin/env bpftrace

/*
 * 简化版CUDA函数监控脚本
 * 专注监控指定的参数
 */

BEGIN
{
    printf("CUDA函数监控启动\n");
}

// cuModuleGetFunction入口: 保存参数信息
uprobe:/lib64/libcuda.so.1:cuModuleGetFunction
{
    @hfunc_ptr[tid] = arg0;        // CUfunction *hfunc 指针地址
    @func_name[tid] = str(arg2);   // 函数名字符串
}

// cuModuleGetFunction返回: 获取实际CUfunction地址
uretprobe:/lib64/libcuda.so.1:cuModuleGetFunction
{
    if (@hfunc_ptr[tid] != 0 && retval == 0) {
        $cufunction_addr = *(uint64*)@hfunc_ptr[tid];
        printf("cuModuleGetFunction - CUfunction地址: 0x%lx, 函数名: %s\n",
               $cufunction_addr, @func_name[tid]);
    }

    // 清理变量
    delete(@hfunc_ptr[tid]);
    delete(@func_name[tid]);
}

// cuLaunchKernel: 打印第1个参数地址
uprobe:/lib64/libcuda.so.1:cuLaunchKernel
{
    printf("cuLaunchKernel - CUfunction地址: 0x%lx\n", arg0);
}

END
{
    // 清理所有全局变量
    clear(@hfunc_ptr);
    clear(@func_name);
}
