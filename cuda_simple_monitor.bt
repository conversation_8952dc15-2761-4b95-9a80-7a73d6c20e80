#!/usr/bin/env bpftrace

/*
 * 简化版CUDA函数监控脚本
 * 专注监控指定的参数
 */

BEGIN
{
    printf("CUDA函数监控启动\n");
}

// cuModuleGetFunction: 打印第1个参数地址和第3个参数字符串
uprobe:/lib64/libcuda.so.1:cuModuleGetFunction
{
    printf("cuModuleGetFunction - 参数1地址: 0x%lx, 参数3字符串: %s\n", 
           arg0, str(arg2));
}

// cuLaunchKernel: 打印第1个参数地址
uprobe:/lib64/libcuda.so.1:cuLaunchKernel
{
    printf("cuLaunchKernel - 参数1地址: 0x%lx\n", arg0);
}
