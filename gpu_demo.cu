#include <cuda_runtime.h>
#include <stdio.h>
#include <stdlib.h>

// CUDA kernel：向量加法
__global__ void vectorAdd(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] + b[idx];
    }
}

// GPU计算函数 - 每次调用都重新申请和释放显存
float gpuCompute(int n) {
    size_t size = n * sizeof(float);
    
    // 分配主机内存
    float *h_a = (float*)malloc(size);
    float *h_b = (float*)malloc(size);
    float *h_c = (float*)malloc(size);
    
    // 初始化数据
    for (int i = 0; i < n; i++) {
        h_a[i] = (float)(rand() % 100) / 100.0f;
        h_b[i] = (float)(rand() % 100) / 100.0f;
    }
    
    // 分配GPU内存
    float *d_a, *d_b, *d_c;
    cudaMalloc(&d_a, size);
    cudaMalloc(&d_b, size);
    cudaMalloc(&d_c, size);
    
    // 复制数据到GPU
    cudaMemcpy(d_a, h_a, size, cudaMemcpyHostToDevice);
    cudaMemcpy(d_b, h_b, size, cudaMemcpyHostToDevice);
    
    // 设置线程块和网格
    int blockSize = 256;
    int gridSize = (n + blockSize - 1) / blockSize;
    
    // 执行GPU计算
    vectorAdd<<<gridSize, blockSize>>>(d_a, d_b, d_c, n);
    cudaDeviceSynchronize();
    
    // 复制结果回主机
    cudaMemcpy(h_c, d_c, size, cudaMemcpyDeviceToHost);
    
    // 计算结果和
    float sum = 0.0f;
    for (int i = 0; i < n; i++) {
        sum += h_c[i];
    }
    
    // 释放GPU内存
    cudaFree(d_a);
    cudaFree(d_b);
    cudaFree(d_c);
    
    // 释放主机内存
    free(h_a);
    free(h_b);
    free(h_c);
    
    return sum;
}

int main() {
    printf("=== GPU计算Demo - 独立方法版本 ===\n");
    printf("每次调用都重新申请和释放显存\n\n");
    
    // 向量大小
    int n = 1 << 20; // 1048576
    
    printf("开始死循环，不断调用GPU计算函数...\n");
    int iter = 0;
    while (true) {
        // 调用GPU计算函数
        float result = gpuCompute(n);
        
        // 每100次打印一次
        if (iter % 100 == 0) {
            printf("迭代%d，结果和: %.2f\n", iter, result);
        }
        iter++;
    }
    
    printf("运算结束！\n");
    return 0;
}