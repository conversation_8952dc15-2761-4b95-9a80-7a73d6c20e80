# CUDA函数监控脚本使用说明

## 脚本文件

1. **cuda_monitor.bt** - 完整版监控脚本，包含详细信息和错误处理
2. **cuda_simple_monitor.bt** - 简化版脚本，只输出您要求的核心信息

## 使用方法

### 基本使用
```bash
# 监控所有进程的CUDA调用
sudo bpftrace cuda_monitor.bt

# 或使用简化版
sudo bpftrace cuda_simple_monitor.bt
```

### 监控特定进程
```bash
# 监控特定PID的进程
sudo bpftrace -p <PID> cuda_monitor.bt

# 监控特定程序名的进程
sudo bpftrace -c "your_cuda_program" cuda_monitor.bt
```

## 监控内容

### cuModuleGetFunction
- **参数1**: CUfunction *hfunc 的地址
- **参数3**: const char *name 函数名字符串

### cuLaunchKernel  
- **参数1**: CUfunction f 函数句柄地址

## 前置要求

1. **安装bpftrace**:
   ```bash
   # Ubuntu/Debian
   sudo apt install bpftrace
   
   # CentOS/RHEL
   sudo yum install bpftrace
   ```

2. **确保CUDA库存在**:
   ```bash
   ls -la /lib64/libcuda.so.1
   ```

3. **运行权限**: 需要root权限或sudo

## 示例输出

```
cuModuleGetFunction - 参数1地址: 0x7f8b4c001234, 参数3字符串: my_kernel_function
cuLaunchKernel - 参数1地址: 0x7f8b4c001234
```

## 故障排除

1. **找不到libcuda.so.1**: 检查CUDA驱动是否正确安装
2. **权限错误**: 确保使用sudo运行
3. **没有输出**: 确保有CUDA程序在运行

## 自定义修改

如需修改监控的参数或添加其他函数，编辑.bt文件中的uprobe部分。
