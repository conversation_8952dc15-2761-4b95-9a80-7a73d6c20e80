#!/usr/bin/env bpftrace

// CUDA动态库监控脚本
// 监控 /usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so.12 中的方法调用

BEGIN
{
    printf("=== CUDA动态库监控开始 ===\n");
    printf("监控目标: /usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so.12\n");
    printf("监控方法: cudaLaunchKernel, __cudaRegisterFunction\n\n");
}

// 监控 cudaLaunchKernel 方法
uprobe:/usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so.12:cudaLaunchKernel
{    
    printf("  函数指针: %p\n", arg0);
    printf("  网格维度 X: %d\n", arg1);
    printf("  网格维度 Y: %d\n", arg2);
    printf("  网格维度 Z: %d\n", arg3);
    printf("  块维度 X: %d\n", arg4);
    printf("  块维度 Y: %d\n", arg5);
    printf("  块维度 Z: %d\n", arg6);
    printf("  共享内存大小: %d bytes\n", arg7);
    printf("  流: %p\n", arg8);
    printf("  参数: %p\n", arg9);
    printf("  进程: %s (PID: %d, TID: %d)\n", comm, pid, tid);
    printf("  时间: %u\n", nsecs);
    printf("---\n");
}

// 监控 __cudaRegisterFunction 方法
uprobe:/usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so.12:__cudaRegisterFunction
{    
    printf("  函数地址: %p\n", arg0);
    printf("  函数名称指针: %p\n", arg1);
    
    // 尝试读取函数名称字符串（如果arg1指向字符串）
    if (arg1 != 0) {
        printf("  函数名称: %s\n", str(arg1));
    }
    
    printf("  进程: %s (PID: %d, TID: %d)\n", comm, pid, tid);
    printf("  时间: %u\n", nsecs);
    printf("---\n");
}


END
{
    printf("\n=== 监控结束 ===\n");
}