#!/usr/bin/env bpftrace

/*
 * CUDA函数监控脚本
 * 监控 cuLaunchKernel 和 cuModuleGetFunction 函数调用
 * 
 * 使用方法:
 * sudo bpftrace cuda_monitor.bt
 * 
 * 或者指定特定进程:
 * sudo bpftrace -p <PID> cuda_monitor.bt
 */

BEGIN
{
    printf("开始监控CUDA函数调用...\n");
    printf("监控的函数:\n");
    printf("  - cuModuleGetFunction: 打印第1个参数地址和第3个参数字符串\n");
    printf("  - cuLaunchKernel: 打印第1个参数地址\n");
    printf("按 Ctrl+C 停止监控\n\n");
}

// 监控 cuModuleGetFunction 函数入口
// 函数签名: CUresult cuModuleGetFunction(CUfunction *hfunc, CUmodule hmod, const char *name)
uprobe:/lib64/libcuda.so.1:cuModuleGetFunction
{
    $hfunc_ptr = arg0;      // 第一个参数：CUfunction *hfunc 的地址
    $hmod = arg1;           // 第二个参数：CUmodule hmod
    $name_ptr = arg2;       // 第三个参数：const char *name 的指针
    
    // 读取函数名字符串（最多64字节，避免读取过长）
    $name_str = str(arg2, 64);
    
    printf("[%s] cuModuleGetFunction 调用:\n", strftime("%H:%M:%S", nsecs));
    printf("  PID: %d, COMM: %s\n", pid, comm);
    printf("  hfunc地址: 0x%lx\n", $hfunc_ptr);
    printf("  函数名: %s\n", $name_str);
    printf("  模块句柄: 0x%lx\n", $hmod);
    printf("\n");
}

// 监控 cuLaunchKernel 函数入口  
// 函数签名: CUresult cuLaunchKernel(CUfunction f, unsigned int gridDimX, unsigned int gridDimY, unsigned int gridDimZ, ...)
uprobe:/lib64/libcuda.so.1:cuLaunchKernel
{
    $function_handle = arg0;  // 第一个参数：CUfunction f
    $gridDimX = arg1;         // 网格维度X
    $gridDimY = arg2;         // 网格维度Y
    $gridDimZ = arg3;         // 网格维度Z
    
    printf("[%s] cuLaunchKernel 调用:\n", strftime("%H:%M:%S", nsecs));
    printf("  PID: %d, COMM: %s\n", pid, comm);
    printf("  函数句柄地址: 0x%lx\n", $function_handle);
    printf("  网格维度: (%d, %d, %d)\n", $gridDimX, $gridDimY, $gridDimZ);
    printf("\n");
}

// 可选：监控函数返回值（如果需要的话）
uretprobe:/lib64/libcuda.so.1:cuModuleGetFunction
{
    $retval = retval;
    if ($retval != 0) {
        printf("[%s] cuModuleGetFunction 返回错误: %d\n", strftime("%H:%M:%S", nsecs), $retval);
    }
}

uretprobe:/lib64/libcuda.so.1:cuLaunchKernel
{
    $retval = retval;
    if ($retval != 0) {
        printf("[%s] cuLaunchKernel 返回错误: %d\n", strftime("%H:%M:%S", nsecs), $retval);
    }
}

// 统计信息
interval:s:10
{
    printf("=== 统计信息 ===\n");
    printf("监控时间: %s\n", strftime("%Y-%m-%d %H:%M:%S", nsecs));
    printf("继续监控中...\n\n");
}

END
{
    printf("\n监控结束\n");
}
