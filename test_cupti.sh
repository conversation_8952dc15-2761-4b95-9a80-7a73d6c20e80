#!/bin/bash

# CUPTI Demo 测试脚本

echo "=== CUPTI Demo 测试脚本 ==="
echo ""

# 检查环境
echo "1. 检查环境..."
make check-env
echo ""

# 编译所有程序
echo "2. 编译程序..."
if make all; then
    echo "✓ 编译成功"
else
    echo "✗ 编译失败"
    exit 1
fi
echo ""

# 设置库路径
export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda/extras/CUPTI/lib64

# 测试Activity API
echo "3. 测试CUPTI Activity API..."
echo "----------------------------------------"
if ./cupti_activity_demo; then
    echo "✓ Activity API测试成功"
else
    echo "✗ Activity API测试失败"
fi
echo ""

# 测试Callback API
echo "4. 测试CUPTI Callback API..."
echo "----------------------------------------"
if ./cupti_callback_demo; then
    echo "✓ Callback API测试成功"
else
    echo "✗ Callback API测试失败"
fi
echo ""

# 测试性能分析器
echo "5. 测试CUPTI 性能分析器..."
echo "----------------------------------------"
if ./cupti_profiler; then
    echo "✓ 性能分析器测试成功"
else
    echo "✗ 性能分析器测试失败"
fi
echo ""

echo "=== 测试完成 ==="
echo ""
echo "如果遇到权限问题，请尝试使用sudo运行："
echo "sudo ./test_cupti.sh"
