# CUPTI Demo Makefile

# CUDA和CUPTI路径配置
CUDA_PATH ?= /usr/local/cuda
CUPTI_PATH ?= $(CUDA_PATH)/extras/CUPTI

# 编译器配置
NVCC = $(CUDA_PATH)/bin/nvcc
CXX = g++

# 编译标志
NVCC_FLAGS = -std=c++11 -O2 -g
CUDA_ARCH = -arch=sm_50  # 根据您的GPU架构调整

# 包含路径
INCLUDES = -I$(CUDA_PATH)/include -I$(CUPTI_PATH)/include

# 库路径和链接库
CUDA_LIBS = -L$(CUDA_PATH)/lib64 -lcudart
CUPTI_LIBS = -L$(CUPTI_PATH)/lib64 -lcupti

# 所有库
LIBS = $(CUDA_LIBS) $(CUPTI_LIBS) -ldl

# 目标文件
TARGETS = cupti_activity_demo cupti_callback_demo cupti_profiler gpu_demo

# 默认目标
all: $(TARGETS)

# 编译规则
cupti_activity_demo: cupti_activity_demo.cu
	$(NVCC) $(NVCC_FLAGS) $(CUDA_ARCH) $(INCLUDES) -o $@ $< $(LIBS)

cupti_callback_demo: cupti_callback_demo.cu
	$(NVCC) $(NVCC_FLAGS) $(CUDA_ARCH) $(INCLUDES) -o $@ $< $(LIBS)

cupti_profiler: cupti_profiler.cu
	$(NVCC) $(NVCC_FLAGS) $(CUDA_ARCH) $(INCLUDES) -o $@ $< $(LIBS)

gpu_demo: gpu_demo.cu
	$(NVCC) $(NVCC_FLAGS) $(CUDA_ARCH) $(INCLUDES) -o $@ $< $(CUDA_LIBS)

# 清理
clean:
	rm -f $(TARGETS) *.o

# 运行示例
run-activity: cupti_activity_demo
	@echo "=== 运行CUPTI Activity API Demo ==="
	./cupti_activity_demo

run-callback: cupti_callback_demo
	@echo "=== 运行CUPTI Callback API Demo ==="
	./cupti_callback_demo

run-profiler: cupti_profiler
	@echo "=== 运行CUPTI 高级性能分析器 ==="
	./cupti_profiler

run-gpu: gpu_demo
	@echo "=== 运行GPU Demo (用于测试监控) ==="
	timeout 10s ./gpu_demo || true

# 测试所有示例
test-all: all
	@echo "测试所有CUPTI示例..."
	@echo ""
	$(MAKE) run-activity
	@echo ""
	$(MAKE) run-callback
	@echo ""
	$(MAKE) run-profiler

# 检查CUPTI环境
check-env:
	@echo "检查CUPTI环境..."
	@echo "CUDA路径: $(CUDA_PATH)"
	@echo "CUPTI路径: $(CUPTI_PATH)"
	@echo ""
	@echo "检查CUDA安装:"
	@ls -la $(CUDA_PATH)/bin/nvcc 2>/dev/null || echo "错误: 找不到nvcc"
	@echo ""
	@echo "检查CUPTI库:"
	@ls -la $(CUPTI_PATH)/lib64/libcupti.so* 2>/dev/null || echo "错误: 找不到CUPTI库"
	@echo ""
	@echo "检查CUPTI头文件:"
	@ls -la $(CUPTI_PATH)/include/cupti.h 2>/dev/null || echo "错误: 找不到CUPTI头文件"

# 帮助信息
help:
	@echo "CUPTI Demo Makefile"
	@echo ""
	@echo "可用目标:"
	@echo "  all              - 编译所有示例"
	@echo "  cupti_activity_demo - 编译Activity API示例"
	@echo "  cupti_callback_demo - 编译Callback API示例"
	@echo "  cupti_profiler   - 编译高级性能分析器"
	@echo "  gpu_demo         - 编译GPU测试程序"
	@echo ""
	@echo "运行目标:"
	@echo "  run-activity     - 运行Activity API示例"
	@echo "  run-callback     - 运行Callback API示例"
	@echo "  run-profiler     - 运行性能分析器"
	@echo "  run-gpu          - 运行GPU测试程序(10秒)"
	@echo "  test-all         - 测试所有示例"
	@echo ""
	@echo "其他:"
	@echo "  check-env        - 检查CUPTI环境"
	@echo "  clean            - 清理编译文件"
	@echo "  help             - 显示此帮助信息"
	@echo ""
	@echo "环境变量:"
	@echo "  CUDA_PATH        - CUDA安装路径 (默认: /usr/local/cuda)"
	@echo "  CUPTI_PATH       - CUPTI路径 (默认: \$$CUDA_PATH/extras/CUPTI)"

.PHONY: all clean run-activity run-callback run-profiler run-gpu test-all check-env help
