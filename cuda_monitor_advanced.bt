#!/usr/bin/env bpftrace

// 高级CUDA动态库监控脚本
// 监控 /usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so 中的方法调用

BEGIN
{
    printf("=== 高级CUDA动态库监控开始 ===\n");
    printf("监控目标: /usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so\n");
    printf("监控方法: cudaLaunchKernel, __cudaRegisterFunction\n");
    printf("按 Ctrl+C 停止监控\n\n");
}

// 全局变量统计
global cudaLaunchKernel_count = 0;
global cudaRegisterFunction_count = 0;

// 监控 cudaLaunchKernel 方法
uprobe:/usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so:cudaLaunchKernel
{
    cudaLaunchKernel_count++;
    
    printf("=== cudaLaunchKernel 调用 #%d ===\n", cudaLaunchKernel_count);
    printf("  函数指针: %p\n", arg0);
    printf("  网格维度 X: %d\n", arg1);
    printf("  网格维度 Y: %d\n", arg2);
    printf("  网格维度 Z: %d\n", arg3);
    printf("  块维度 X: %d\n", arg4);
    printf("  块维度 Y: %d\n", arg5);
    printf("  块维度 Z: %d\n", arg6);
    printf("  共享内存大小: %d bytes\n", arg7);
    printf("  流: %p\n", arg8);
    printf("  参数: %p\n", arg9);
    printf("  进程: %s (PID: %d, TID: %d)\n", comm, pid, tid);
    printf("  时间: %u\n", nsecs);
    printf("---\n");
}

// 监控 __cudaRegisterFunction 方法
uprobe:/usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so:__cudaRegisterFunction
{
    cudaRegisterFunction_count++;
    
    printf("=== __cudaRegisterFunction 调用 #%d ===\n", cudaRegisterFunction_count);
    printf("  第一个参数: %p\n", arg0);
    printf("  第二个参数: %p\n", arg1);
    printf("  进程: %s (PID: %d, TID: %d)\n", comm, pid, tid);
    printf("  时间: %u\n", nsecs);
    printf("---\n");
}

// 监控方法退出
uretprobe:/usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so:cudaLaunchKernel
{
    printf("cudaLaunchKernel 返回: %d (0=成功, 其他=错误)\n", retval);
    printf("---\n");
}

uretprobe:/usr/local/cuda-12.2/targets/x86_64-linux/lib/libcudart.so:__cudaRegisterFunction
{
    printf("__cudaRegisterFunction 返回: %d\n", retval);
    printf("---\n");
}

// 每秒打印统计信息
interval:s:1
{
    printf("统计信息 - cudaLaunchKernel: %d 次, __cudaRegisterFunction: %d 次\n", 
           cudaLaunchKernel_count, cudaRegisterFunction_count);
}

END
{
    printf("\n=== 监控结束 ===\n");
    printf("总调用次数:\n");
    printf("  cudaLaunchKernel: %d 次\n", cudaLaunchKernel_count);
    printf("  __cudaRegisterFunction: %d 次\n", cudaRegisterFunction_count);
} 