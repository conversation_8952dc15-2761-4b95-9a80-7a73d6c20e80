#include <cuda_runtime.h>
#include <cupti.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define CUPTI_CALL(call)                                                \
do {                                                                    \
    CUptiResult _status = call;                                         \
    if (_status != CUPTI_SUCCESS) {                                     \
        const char *errstr;                                             \
        cuptiGetResultString(_status, &errstr);                         \
        fprintf(stderr, "%s:%d: error: function %s failed with error %s.\n", \
                __FILE__, __LINE__, #call, errstr);                    \
        exit(-1);                                                       \
    }                                                                   \
} while (0)

// 全局统计变量
static int g_kernelCount = 0;
static int g_memcpyCount = 0;
static int g_mallocCount = 0;
static int g_freeCount = 0;

// CUDA kernel
__global__ void vectorAdd(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] + b[idx];
    }
}

// 获取当前时间戳（纳秒）
uint64_t getCurrentTimestamp() {
    uint64_t timestamp;
    cuptiGetTimestamp(&timestamp);
    return timestamp;
}

// Runtime API回调函数
void CUPTIAPI runtimeCallback(void *userdata, CUpti_CallbackDomain domain,
                             CUpti_CallbackId cbid, const void *cbdata) {
    const CUpti_CallbackData *cbInfo = (CUpti_CallbackData *)cbdata;
    
    if (domain == CUPTI_CB_DOMAIN_RUNTIME_API) {
        switch (cbid) {
            case CUPTI_RUNTIME_TRACE_CBID_cudaMalloc_v3020: {
                if (cbInfo->callbackSite == CUPTI_API_ENTER) {
                    printf("[ENTER] cudaMalloc\n");
                } else if (cbInfo->callbackSite == CUPTI_API_EXIT) {
                    cudaMalloc_v3020_params *params = (cudaMalloc_v3020_params *)cbInfo->functionParams;
                    printf("[EXIT]  cudaMalloc: 地址=0x%p, 大小=%zu bytes\n", 
                           *(params->devPtr), params->size);
                    g_mallocCount++;
                }
                break;
            }
            case CUPTI_RUNTIME_TRACE_CBID_cudaFree_v3020: {
                if (cbInfo->callbackSite == CUPTI_API_ENTER) {
                    cudaFree_v3020_params *params = (cudaFree_v3020_params *)cbInfo->functionParams;
                    printf("[ENTER] cudaFree: 地址=0x%p\n", params->devPtr);
                } else if (cbInfo->callbackSite == CUPTI_API_EXIT) {
                    printf("[EXIT]  cudaFree 完成\n");
                    g_freeCount++;
                }
                break;
            }
            case CUPTI_RUNTIME_TRACE_CBID_cudaMemcpy_v3020: {
                if (cbInfo->callbackSite == CUPTI_API_ENTER) {
                    cudaMemcpy_v3020_params *params = (cudaMemcpy_v3020_params *)cbInfo->functionParams;
                    const char *kindStr = "";
                    switch (params->kind) {
                        case cudaMemcpyHostToDevice: kindStr = "H2D"; break;
                        case cudaMemcpyDeviceToHost: kindStr = "D2H"; break;
                        case cudaMemcpyDeviceToDevice: kindStr = "D2D"; break;
                        case cudaMemcpyHostToHost: kindStr = "H2H"; break;
                        default: kindStr = "Unknown"; break;
                    }
                    printf("[ENTER] cudaMemcpy: %s, 大小=%zu bytes\n", kindStr, params->count);
                } else if (cbInfo->callbackSite == CUPTI_API_EXIT) {
                    printf("[EXIT]  cudaMemcpy 完成\n");
                    g_memcpyCount++;
                }
                break;
            }
            case CUPTI_RUNTIME_TRACE_CBID_cudaLaunch_v3020: {
                if (cbInfo->callbackSite == CUPTI_API_ENTER) {
                    printf("[ENTER] cudaLaunch (旧版本)\n");
                } else if (cbInfo->callbackSite == CUPTI_API_EXIT) {
                    printf("[EXIT]  cudaLaunch 完成\n");
                    g_kernelCount++;
                }
                break;
            }
            case CUPTI_RUNTIME_TRACE_CBID_cudaLaunchKernel_v7000: {
                if (cbInfo->callbackSite == CUPTI_API_ENTER) {
                    cudaLaunchKernel_v7000_params *params = (cudaLaunchKernel_v7000_params *)cbInfo->functionParams;
                    printf("[ENTER] cudaLaunchKernel: grid=(%u,%u,%u), block=(%u,%u,%u)\n",
                           params->gridDim.x, params->gridDim.y, params->gridDim.z,
                           params->blockDim.x, params->blockDim.y, params->blockDim.z);
                } else if (cbInfo->callbackSite == CUPTI_API_EXIT) {
                    printf("[EXIT]  cudaLaunchKernel 完成\n");
                    g_kernelCount++;
                }
                break;
            }
            case CUPTI_RUNTIME_TRACE_CBID_cudaDeviceSynchronize_v3020: {
                if (cbInfo->callbackSite == CUPTI_API_ENTER) {
                    printf("[ENTER] cudaDeviceSynchronize\n");
                } else if (cbInfo->callbackSite == CUPTI_API_EXIT) {
                    printf("[EXIT]  cudaDeviceSynchronize 完成\n");
                }
                break;
            }
            default:
                // 其他API调用
                break;
        }
    }
}

// Driver API回调函数
void CUPTIAPI driverCallback(void *userdata, CUpti_CallbackDomain domain,
                            CUpti_CallbackId cbid, const void *cbdata) {
    const CUpti_CallbackData *cbInfo = (CUpti_CallbackData *)cbdata;
    
    if (domain == CUPTI_CB_DOMAIN_DRIVER_API) {
        switch (cbid) {
            case CUPTI_DRIVER_TRACE_CBID_cuLaunchKernel: {
                if (cbInfo->callbackSite == CUPTI_API_ENTER) {
                    cuLaunchKernel_params *params = (cuLaunchKernel_params *)cbInfo->functionParams;
                    printf("[DRIVER ENTER] cuLaunchKernel: grid=(%u,%u,%u), block=(%u,%u,%u)\n",
                           params->gridDimX, params->gridDimY, params->gridDimZ,
                           params->blockDimX, params->blockDimY, params->blockDimZ);
                } else if (cbInfo->callbackSite == CUPTI_API_EXIT) {
                    printf("[DRIVER EXIT]  cuLaunchKernel 完成\n");
                }
                break;
            }
            case CUPTI_DRIVER_TRACE_CBID_cuModuleGetFunction: {
                if (cbInfo->callbackSite == CUPTI_API_ENTER) {
                    cuModuleGetFunction_params *params = (cuModuleGetFunction_params *)cbInfo->functionParams;
                    printf("[DRIVER ENTER] cuModuleGetFunction: 函数名=%s\n", params->name);
                } else if (cbInfo->callbackSite == CUPTI_API_EXIT) {
                    printf("[DRIVER EXIT]  cuModuleGetFunction 完成\n");
                }
                break;
            }
            default:
                break;
        }
    }
}

// 初始化CUPTI回调
void initCuptiCallbacks() {
    printf("初始化CUPTI回调...\n");
    
    CUpti_SubscriberHandle subscriber;
    
    // 创建订阅者
    CUPTI_CALL(cuptiSubscribe(&subscriber, (CUpti_CallbackFunc)runtimeCallback, NULL));
    
    // 启用Runtime API回调
    CUPTI_CALL(cuptiEnableCallback(1, subscriber, CUPTI_CB_DOMAIN_RUNTIME_API,
                                   CUPTI_RUNTIME_TRACE_CBID_cudaMalloc_v3020));
    CUPTI_CALL(cuptiEnableCallback(1, subscriber, CUPTI_CB_DOMAIN_RUNTIME_API,
                                   CUPTI_RUNTIME_TRACE_CBID_cudaFree_v3020));
    CUPTI_CALL(cuptiEnableCallback(1, subscriber, CUPTI_CB_DOMAIN_RUNTIME_API,
                                   CUPTI_RUNTIME_TRACE_CBID_cudaMemcpy_v3020));
    CUPTI_CALL(cuptiEnableCallback(1, subscriber, CUPTI_CB_DOMAIN_RUNTIME_API,
                                   CUPTI_RUNTIME_TRACE_CBID_cudaLaunchKernel_v7000));
    CUPTI_CALL(cuptiEnableCallback(1, subscriber, CUPTI_CB_DOMAIN_RUNTIME_API,
                                   CUPTI_RUNTIME_TRACE_CBID_cudaDeviceSynchronize_v3020));
    
    // 创建Driver API订阅者
    CUpti_SubscriberHandle driverSubscriber;
    CUPTI_CALL(cuptiSubscribe(&driverSubscriber, (CUpti_CallbackFunc)driverCallback, NULL));
    
    // 启用Driver API回调
    CUPTI_CALL(cuptiEnableCallback(1, driverSubscriber, CUPTI_CB_DOMAIN_DRIVER_API,
                                   CUPTI_DRIVER_TRACE_CBID_cuLaunchKernel));
    CUPTI_CALL(cuptiEnableCallback(1, driverSubscriber, CUPTI_CB_DOMAIN_DRIVER_API,
                                   CUPTI_DRIVER_TRACE_CBID_cuModuleGetFunction));
    
    printf("CUPTI回调初始化完成\n\n");
}

// 执行CUDA操作
void runCudaOperations() {
    printf("=== 执行CUDA操作 ===\n");
    
    const int n = 1024 * 1024;
    const int size = n * sizeof(float);
    
    // 分配主机内存
    float *h_a = (float*)malloc(size);
    float *h_b = (float*)malloc(size);
    float *h_c = (float*)malloc(size);
    
    // 初始化数据
    for (int i = 0; i < n; i++) {
        h_a[i] = (float)i;
        h_b[i] = (float)(i * 2);
    }
    
    // 分配GPU内存
    float *d_a, *d_b, *d_c;
    cudaMalloc(&d_a, size);
    cudaMalloc(&d_b, size);
    cudaMalloc(&d_c, size);
    
    // 内存拷贝
    cudaMemcpy(d_a, h_a, size, cudaMemcpyHostToDevice);
    cudaMemcpy(d_b, h_b, size, cudaMemcpyHostToDevice);
    
    // 执行kernel
    int blockSize = 256;
    int gridSize = (n + blockSize - 1) / blockSize;
    
    printf("\n--- 启动kernel ---\n");
    vectorAdd<<<gridSize, blockSize>>>(d_a, d_b, d_c, n);
    
    // 同步
    printf("\n--- 同步设备 ---\n");
    cudaDeviceSynchronize();
    
    // 拷贝结果
    printf("\n--- 拷贝结果 ---\n");
    cudaMemcpy(h_c, d_c, size, cudaMemcpyDeviceToHost);
    
    // 验证结果
    bool success = true;
    for (int i = 0; i < 10; i++) {
        if (h_c[i] != h_a[i] + h_b[i]) {
            success = false;
            break;
        }
    }
    
    printf("\n计算结果: %s\n", success ? "正确" : "错误");
    
    // 清理
    printf("\n--- 清理GPU内存 ---\n");
    cudaFree(d_a);
    cudaFree(d_b);
    cudaFree(d_c);
    
    free(h_a);
    free(h_b);
    free(h_c);
    
    printf("CUDA操作完成\n\n");
}

// 打印统计信息
void printStatistics() {
    printf("=== 统计信息 ===\n");
    printf("Kernel启动次数: %d\n", g_kernelCount);
    printf("内存拷贝次数: %d\n", g_memcpyCount);
    printf("内存分配次数: %d\n", g_mallocCount);
    printf("内存释放次数: %d\n", g_freeCount);
    printf("\n");
}

int main() {
    printf("=== CUPTI Callback API Demo ===\n\n");
    
    // 初始化CUDA
    cudaSetDevice(0);
    
    // 初始化CUPTI回调
    initCuptiCallbacks();
    
    // 执行CUDA操作
    runCudaOperations();
    
    // 打印统计信息
    printStatistics();
    
    printf("Demo完成\n");
    return 0;
}
