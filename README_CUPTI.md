# CUPTI Demo 项目

本项目包含多个CUPTI (CUDA Profiling Tools Interface) 示例，用于演示如何采集和分析CUDA程序的性能数据。

## 项目文件

### CUPTI示例程序
1. **cupti_activity_demo.cu** - CUPTI Activity API基础示例
2. **cupti_callback_demo.cu** - CUPTI Callback API示例  
3. **cupti_profiler.cu** - 高级性能分析器
4. **gpu_demo.cu** - 用于测试的CUDA程序

### 构建和配置
- **Makefile** - 编译配置文件
- **README_CUPTI.md** - 本文档

## 功能特性

### 1. CUPTI Activity API Demo (cupti_activity_demo.cu)
- **功能**: 使用Activity API采集GPU活动数据
- **监控内容**:
  - Kernel执行信息（名称、执行时间、网格/线程块配置）
  - 内存拷贝操作（类型、大小、带宽）
  - 内存设置操作
- **特点**: 异步数据收集，低开销

### 2. CUPTI Callback API Demo (cupti_callback_demo.cu)
- **功能**: 使用Callback API监控CUDA Runtime和Driver API调用
- **监控内容**:
  - Runtime API调用（cudaMalloc, cudaFree, cudaMemcpy等）
  - Driver API调用（cuLaunchKernel, cuModuleGetFunction等）
  - API调用的进入和退出事件
- **特点**: 同步监控，可获取API参数

### 3. 高级性能分析器 (cupti_profiler.cu)
- **功能**: 完整的性能分析工具
- **分析内容**:
  - Kernel性能统计（调用次数、执行时间分布、配置信息）
  - 内存拷贝性能分析（带宽计算、时间统计）
  - 详细的性能报告生成
- **特点**: 自动统计分析，生成专业报告

## 环境要求

### 必需组件
- CUDA Toolkit (推荐11.0+)
- CUPTI库 (通常包含在CUDA Toolkit中)
- 支持CUDA的GPU
- Linux操作系统

### 检查环境
```bash
# 检查CUDA安装
nvcc --version

# 检查CUPTI库
ls /usr/local/cuda/extras/CUPTI/lib64/

# 检查环境配置
make check-env
```

## 编译和运行

### 快速开始
```bash
# 编译所有示例
make all

# 运行所有测试
make test-all
```

### 单独编译和运行
```bash
# 编译Activity API示例
make cupti_activity_demo
make run-activity

# 编译Callback API示例  
make cupti_callback_demo
make run-callback

# 编译性能分析器
make cupti_profiler
make run-profiler
```

### 自定义CUDA路径
```bash
# 如果CUDA安装在非标准位置
make CUDA_PATH=/opt/cuda all
```

## 示例输出

### Activity API Demo输出
```
=== Activity Buffer 完成回调 ===
Kernel: vectorAdd(float*, float*, float*, int)
  Device: 0, Context: 1, Stream: 7
  Grid: (4096,1,1), Block: (256,1,1)
  时间: 1234567890 - 1234567950 ns (持续时间: 60 ns)
  寄存器/线程: 16, 共享内存: 0 bytes

内存拷贝:
  类型: 1, 大小: 4194304 bytes
  时间: 1234567800 - 1234567850 ns (持续时间: 50 ns)
  带宽: 83.89 GB/s
```

### 性能分析器输出
```
============================================================
                    性能分析报告
============================================================

【Kernel性能统计】
Kernel名称                      调用次数     总时间(μs)     平均时间(μs)     最小时间(μs)     最大时间(μs)
------------------------------------------------------------------------------------------
vectorAdd                           3         150.25          50.08          48.12          52.34
matrixMul                           2        2345.67        1172.84        1165.23        1180.44

【内存拷贝性能统计】
拷贝类型              调用次数     总时间(μs)     总大小(MB)   平均带宽(GB/s)     最小时间(μs)     最大时间(μs)
----------------------------------------------------------------------------------------------------
Host->Device               6         234.56          24.00         102.34         38.45          42.11
Device->Host               3         123.45          12.00          97.23         40.12          43.21
```

## 使用场景

### 1. 性能调优
- 识别性能瓶颈
- 分析kernel执行效率
- 优化内存传输

### 2. 程序分析
- 监控CUDA API调用
- 分析程序执行流程
- 调试GPU程序

### 3. 基准测试
- 比较不同实现的性能
- 生成性能报告
- 验证优化效果

## 高级用法

### 1. 集成到现有项目
```cpp
// 在您的CUDA程序中添加
#include "cupti_profiler.h"

int main() {
    initCupti();           // 初始化CUPTI
    
    // 您的CUDA代码
    yourCudaFunction();
    
    finalizeCupti();       // 生成报告
    return 0;
}
```

### 2. 自定义监控
- 修改Activity类型选择
- 添加自定义统计指标
- 实现特定的分析逻辑

### 3. 与其他工具结合
- 配合bpftrace脚本使用
- 集成到CI/CD流程
- 导出数据到分析工具

## 故障排除

### 常见问题

1. **编译错误: 找不到cupti.h**
   ```bash
   # 检查CUPTI路径
   export CUPTI_PATH=/usr/local/cuda/extras/CUPTI
   ```

2. **运行时错误: 找不到libcupti.so**
   ```bash
   # 添加库路径
   export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/usr/local/cuda/extras/CUPTI/lib64
   ```

3. **权限错误**
   ```bash
   # 某些系统需要root权限运行CUPTI
   sudo ./cupti_activity_demo
   ```

### 调试技巧
- 使用`make check-env`检查环境
- 查看CUDA错误信息
- 检查GPU驱动版本兼容性

## 扩展开发

### 添加新的监控功能
1. 在相应的回调函数中添加处理逻辑
2. 更新统计数据结构
3. 修改报告生成函数

### 性能优化建议
- 合理设置buffer大小
- 选择必要的Activity类型
- 避免频繁的数据刷新

## 参考资料

- [CUPTI官方文档](https://docs.nvidia.com/cupti/)
- [CUDA Profiling Guide](https://docs.nvidia.com/cuda/profiler-users-guide/)
- [CUPTI示例代码](https://github.com/NVIDIA/cuda-samples)
