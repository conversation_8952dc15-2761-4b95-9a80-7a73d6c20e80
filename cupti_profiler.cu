#include <cuda_runtime.h>
#include <cupti.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <map>
#include <vector>
#include <string>

#define CUPTI_CALL(call)                                                \
do {                                                                    \
    CUptiResult _status = call;                                         \
    if (_status != CUPTI_SUCCESS) {                                     \
        const char *errstr;                                             \
        cuptiGetResultString(_status, &errstr);                         \
        fprintf(stderr, "%s:%d: error: function %s failed with error %s.\n", \
                __FILE__, __LINE__, #call, errstr);                    \
        exit(-1);                                                       \
    }                                                                   \
} while (0)

#define BUF_SIZE (32 * 1024)
#define ALIGN_SIZE (8)
#define ALIGN_BUFFER(buffer, align)                                     \
  (((uintptr_t) (buffer) & ((align)-1)) ? ((buffer) + (align) - ((uintptr_t) (buffer) & ((align)-1))) : (buffer))

// 性能统计结构
struct KernelStats {
    std::string name;
    int count;
    uint64_t totalTime;
    uint64_t minTime;
    uint64_t maxTime;
    uint32_t gridX, gridY, gridZ;
    uint32_t blockX, blockY, blockZ;
    uint32_t registersPerThread;
    uint32_t sharedMemory;
    
    KernelStats() : count(0), totalTime(0), minTime(UINT64_MAX), maxTime(0),
                   gridX(0), gridY(0), gridZ(0), blockX(0), blockY(0), blockZ(0),
                   registersPerThread(0), sharedMemory(0) {}
};

struct MemcpyStats {
    int count;
    uint64_t totalTime;
    uint64_t totalBytes;
    uint64_t minTime;
    uint64_t maxTime;
    
    MemcpyStats() : count(0), totalTime(0), totalBytes(0), 
                   minTime(UINT64_MAX), maxTime(0) {}
};

// 全局统计变量
static std::map<std::string, KernelStats> g_kernelStats;
static std::map<int, MemcpyStats> g_memcpyStats; // key: copyKind
static uint8_t *s_pActivityBuffer;
static uint8_t *s_pAlignedActivityBuffer;

// CUDA kernels for testing
__global__ void vectorAdd(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] + b[idx];
    }
}

__global__ void vectorMul(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] * b[idx];
    }
}

__global__ void matrixMul(float* a, float* b, float* c, int width) {
    int row = blockIdx.y * blockDim.y + threadIdx.y;
    int col = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (row < width && col < width) {
        float sum = 0.0f;
        for (int k = 0; k < width; k++) {
            sum += a[row * width + k] * b[k * width + col];
        }
        c[row * width + col] = sum;
    }
}

// Activity buffer 请求回调
void CUPTIAPI bufferRequested(uint8_t **buffer, size_t *size, size_t *maxNumRecords) {
    *size = BUF_SIZE;
    *buffer = s_pAlignedActivityBuffer;
    *maxNumRecords = 0;
}

// Activity buffer 完成回调
void CUPTIAPI bufferCompleted(CUcontext ctx, uint32_t streamId, uint8_t *buffer, size_t size, size_t validSize) {
    CUptiResult status;
    CUptiActivity *record = NULL;
    
    if (validSize > 0) {
        do {
            status = cuptiActivityGetNextRecord(buffer, validSize, &record);
            if (status == CUPTI_SUCCESS) {
                switch (record->kind) {
                    case CUPTI_ACTIVITY_KIND_KERNEL:
                    case CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL: {
                        CUptiActivityKernel4 *kernel = (CUptiActivityKernel4 *)record;
                        std::string kernelName(kernel->name);
                        uint64_t duration = kernel->end - kernel->start;
                        
                        // 更新kernel统计
                        KernelStats &stats = g_kernelStats[kernelName];
                        if (stats.count == 0) {
                            stats.name = kernelName;
                            stats.gridX = kernel->gridX;
                            stats.gridY = kernel->gridY;
                            stats.gridZ = kernel->gridZ;
                            stats.blockX = kernel->blockX;
                            stats.blockY = kernel->blockY;
                            stats.blockZ = kernel->blockZ;
                            stats.registersPerThread = kernel->registersPerThread;
                            stats.sharedMemory = kernel->sharedMemoryConfig;
                        }
                        
                        stats.count++;
                        stats.totalTime += duration;
                        if (duration < stats.minTime) stats.minTime = duration;
                        if (duration > stats.maxTime) stats.maxTime = duration;
                        break;
                    }
                    case CUPTI_ACTIVITY_KIND_MEMCPY: {
                        CUptiActivityMemcpy *memcpy = (CUptiActivityMemcpy *)record;
                        uint64_t duration = memcpy->end - memcpy->start;
                        
                        // 更新memcpy统计
                        MemcpyStats &stats = g_memcpyStats[memcpy->copyKind];
                        stats.count++;
                        stats.totalTime += duration;
                        stats.totalBytes += memcpy->bytes;
                        if (duration < stats.minTime) stats.minTime = duration;
                        if (duration > stats.maxTime) stats.maxTime = duration;
                        break;
                    }
                    default:
                        break;
                }
            } else if (status == CUPTI_ERROR_MAX_LIMIT_REACHED) {
                break;
            } else {
                CUPTI_CALL(status);
            }
        } while (1);
    }
}

// 初始化CUPTI
void initCupti() {
    printf("初始化CUPTI性能分析器...\n");
    
    // 分配activity buffer
    s_pActivityBuffer = (uint8_t *)malloc(BUF_SIZE + ALIGN_SIZE);
    if (s_pActivityBuffer == NULL) {
        printf("错误: 无法分配activity buffer\n");
        exit(-1);
    }
    
    s_pAlignedActivityBuffer = ALIGN_BUFFER(s_pActivityBuffer, ALIGN_SIZE);
    
    // 注册回调函数
    CUPTI_CALL(cuptiActivityRegisterCallbacks(bufferRequested, bufferCompleted));
    
    // 启用activity类型
    CUPTI_CALL(cuptiActivityEnable(CUPTI_ACTIVITY_KIND_KERNEL));
    CUPTI_CALL(cuptiActivityEnable(CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL));
    CUPTI_CALL(cuptiActivityEnable(CUPTI_ACTIVITY_KIND_MEMCPY));
    
    printf("CUPTI性能分析器初始化完成\n\n");
}

// 清理CUPTI
void finalizeCupti() {
    printf("\n正在收集最终的性能数据...\n");
    
    // 刷新所有activity记录
    CUPTI_CALL(cuptiActivityFlushAll(0));
    
    // 禁用activity
    CUPTI_CALL(cuptiActivityDisable(CUPTI_ACTIVITY_KIND_KERNEL));
    CUPTI_CALL(cuptiActivityDisable(CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL));
    CUPTI_CALL(cuptiActivityDisable(CUPTI_ACTIVITY_KIND_MEMCPY));
    
    // 释放buffer
    free(s_pActivityBuffer);
}

// 获取内存拷贝类型名称
const char* getMemcpyKindName(int kind) {
    switch (kind) {
        case cudaMemcpyHostToDevice: return "Host->Device";
        case cudaMemcpyDeviceToHost: return "Device->Host";
        case cudaMemcpyDeviceToDevice: return "Device->Device";
        case cudaMemcpyHostToHost: return "Host->Host";
        default: return "Unknown";
    }
}

// 打印性能报告
void printPerformanceReport() {
    printf("\n============================================================\n");
    printf("                    性能分析报告\n");
    printf("============================================================\n");
    
    // Kernel统计
    printf("\n【Kernel性能统计】\n");
    printf("%-30s %8s %12s %12s %12s %12s\n", 
           "Kernel名称", "调用次数", "总时间(μs)", "平均时间(μs)", "最小时间(μs)", "最大时间(μs)");
    printf("------------------------------------------------------------------------------------------\n");
    
    for (auto &pair : g_kernelStats) {
        const KernelStats &stats = pair.second;
        double totalTimeUs = stats.totalTime / 1000.0;
        double avgTimeUs = totalTimeUs / stats.count;
        double minTimeUs = stats.minTime / 1000.0;
        double maxTimeUs = stats.maxTime / 1000.0;
        
        printf("%-30s %8d %12.2f %12.2f %12.2f %12.2f\n",
               stats.name.c_str(), stats.count, totalTimeUs, avgTimeUs, minTimeUs, maxTimeUs);
        
        printf("  配置: grid(%u,%u,%u) block(%u,%u,%u) 寄存器/线程:%u 共享内存:%u\n",
               stats.gridX, stats.gridY, stats.gridZ,
               stats.blockX, stats.blockY, stats.blockZ,
               stats.registersPerThread, stats.sharedMemory);
    }
    
    // 内存拷贝统计
    printf("\n【内存拷贝性能统计】\n");
    printf("%-20s %8s %12s %12s %12s %12s %12s\n",
           "拷贝类型", "调用次数", "总时间(μs)", "总大小(MB)", "平均带宽(GB/s)", "最小时间(μs)", "最大时间(μs)");
    printf("----------------------------------------------------------------------------------------------------\n");
    
    for (auto &pair : g_memcpyStats) {
        int kind = pair.first;
        const MemcpyStats &stats = pair.second;
        
        if (stats.count > 0) {
            double totalTimeUs = stats.totalTime / 1000.0;
            double totalSizeMB = stats.totalBytes / (1024.0 * 1024.0);
            double avgBandwidthGBs = (stats.totalBytes / (1024.0 * 1024.0 * 1024.0)) / (stats.totalTime / 1e9);
            double minTimeUs = stats.minTime / 1000.0;
            double maxTimeUs = stats.maxTime / 1000.0;
            
            printf("%-20s %8d %12.2f %12.2f %12.2f %12.2f %12.2f\n",
                   getMemcpyKindName(kind), stats.count, totalTimeUs, totalSizeMB, 
                   avgBandwidthGBs, minTimeUs, maxTimeUs);
        }
    }
    
    printf("\n============================================================\n");
}

// 执行测试工作负载
void runTestWorkload() {
    printf("=== 执行测试工作负载 ===\n");
    
    const int n = 1024 * 1024;  // 1M elements
    const int matSize = 512;    // 512x512 matrix
    
    // 向量操作测试
    printf("1. 向量操作测试...\n");
    {
        size_t size = n * sizeof(float);
        float *h_a = (float*)malloc(size);
        float *h_b = (float*)malloc(size);
        float *h_c = (float*)malloc(size);
        
        for (int i = 0; i < n; i++) {
            h_a[i] = (float)i;
            h_b[i] = (float)(i * 2);
        }
        
        float *d_a, *d_b, *d_c;
        cudaMalloc(&d_a, size);
        cudaMalloc(&d_b, size);
        cudaMalloc(&d_c, size);
        
        // 多次执行以获得统计数据
        for (int iter = 0; iter < 3; iter++) {
            cudaMemcpy(d_a, h_a, size, cudaMemcpyHostToDevice);
            cudaMemcpy(d_b, h_b, size, cudaMemcpyHostToDevice);
            
            int blockSize = 256;
            int gridSize = (n + blockSize - 1) / blockSize;
            
            vectorAdd<<<gridSize, blockSize>>>(d_a, d_b, d_c, n);
            vectorMul<<<gridSize, blockSize>>>(d_a, d_b, d_c, n);
            
            cudaDeviceSynchronize();
            cudaMemcpy(h_c, d_c, size, cudaMemcpyDeviceToHost);
        }
        
        cudaFree(d_a); cudaFree(d_b); cudaFree(d_c);
        free(h_a); free(h_b); free(h_c);
    }
    
    // 矩阵乘法测试
    printf("2. 矩阵乘法测试...\n");
    {
        size_t size = matSize * matSize * sizeof(float);
        float *h_a = (float*)malloc(size);
        float *h_b = (float*)malloc(size);
        float *h_c = (float*)malloc(size);
        
        for (int i = 0; i < matSize * matSize; i++) {
            h_a[i] = (float)(rand() % 100) / 100.0f;
            h_b[i] = (float)(rand() % 100) / 100.0f;
        }
        
        float *d_a, *d_b, *d_c;
        cudaMalloc(&d_a, size);
        cudaMalloc(&d_b, size);
        cudaMalloc(&d_c, size);
        
        cudaMemcpy(d_a, h_a, size, cudaMemcpyHostToDevice);
        cudaMemcpy(d_b, h_b, size, cudaMemcpyHostToDevice);
        
        dim3 blockSize(16, 16);
        dim3 gridSize((matSize + blockSize.x - 1) / blockSize.x,
                      (matSize + blockSize.y - 1) / blockSize.y);
        
        // 执行2次矩阵乘法
        for (int iter = 0; iter < 2; iter++) {
            matrixMul<<<gridSize, blockSize>>>(d_a, d_b, d_c, matSize);
            cudaDeviceSynchronize();
        }
        
        cudaMemcpy(h_c, d_c, size, cudaMemcpyDeviceToHost);
        
        cudaFree(d_a); cudaFree(d_b); cudaFree(d_c);
        free(h_a); free(h_b); free(h_c);
    }
    
    printf("测试工作负载完成\n");
}

int main() {
    printf("=== CUPTI 高级性能分析器 ===\n\n");
    
    // 初始化CUDA
    cudaSetDevice(0);
    
    // 初始化CUPTI
    initCupti();
    
    // 执行测试工作负载
    runTestWorkload();
    
    // 等待所有activity记录完成
    sleep(1);
    
    // 清理CUPTI并收集最终数据
    finalizeCupti();
    
    // 打印性能报告
    printPerformanceReport();
    
    printf("\n性能分析完成\n");
    return 0;
}
