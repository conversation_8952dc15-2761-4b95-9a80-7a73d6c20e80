#include <cuda_runtime.h>
#include <cupti.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#define CUPTI_CALL(call)                                                \
do {                                                                    \
    CUptiResult _status = call;                                         \
    if (_status != CUPTI_SUCCESS) {                                     \
        const char *errstr;                                             \
        cuptiGetResultString(_status, &errstr);                         \
        fprintf(stderr, "%s:%d: error: function %s failed with error %s.\n", \
                __FILE__, __LINE__, #call, errstr);                    \
        exit(-1);                                                       \
    }                                                                   \
} while (0)

#define BUF_SIZE (32 * 1024)
#define ALIGN_SIZE (8)
#define ALIGN_BUFFER(buffer, align)                                     \
  (((uintptr_t) (buffer) & ((align)-1)) ? ((buffer) + (align) - ((uintptr_t) (buffer) & ((align)-1))) : (buffer))

// 全局变量
static uint8_t *s_pActivityBuffer;
static uint8_t *s_pAlignedActivityBuffer;
static size_t s_validBufferSize;

// CUDA kernel：简单的向量加法
__global__ void vectorAdd(float* a, float* b, float* c, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < n) {
        c[idx] = a[idx] + b[idx];
    }
}

// CUDA kernel：矩阵乘法
__global__ void matrixMul(float* a, float* b, float* c, int width) {
    int row = blockIdx.y * blockDim.y + threadIdx.y;
    int col = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (row < width && col < width) {
        float sum = 0.0f;
        for (int k = 0; k < width; k++) {
            sum += a[row * width + k] * b[k * width + col];
        }
        c[row * width + col] = sum;
    }
}

// Activity buffer 请求回调函数
void CUPTIAPI bufferRequested(uint8_t **buffer, size_t *size, size_t *maxNumRecords) {
    *size = BUF_SIZE;
    *buffer = s_pAlignedActivityBuffer;
    *maxNumRecords = 0;
}

// Activity buffer 完成回调函数
void CUPTIAPI bufferCompleted(CUcontext ctx, uint32_t streamId, uint8_t *buffer, size_t size, size_t validSize) {
    CUptiResult status;
    CUptiActivity *record = NULL;
    
    printf("\n=== Activity Buffer 完成回调 ===\n");
    printf("Context: %p, Stream: %u, Buffer size: %zu, Valid size: %zu\n", 
           ctx, streamId, size, validSize);
    
    if (validSize > 0) {
        do {
            status = cuptiActivityGetNextRecord(buffer, validSize, &record);
            if (status == CUPTI_SUCCESS) {
                switch (record->kind) {
                    case CUPTI_ACTIVITY_KIND_KERNEL:
                    case CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL: {
                        CUptiActivityKernel4 *kernel = (CUptiActivityKernel4 *)record;
                        printf("Kernel: %s\n", kernel->name);
                        printf("  Device: %u, Context: %u, Stream: %u\n", 
                               kernel->deviceId, kernel->contextId, kernel->streamId);
                        printf("  Grid: (%u,%u,%u), Block: (%u,%u,%u)\n",
                               kernel->gridX, kernel->gridY, kernel->gridZ,
                               kernel->blockX, kernel->blockY, kernel->blockZ);
                        printf("  时间: %llu - %llu ns (持续时间: %llu ns)\n",
                               (unsigned long long)kernel->start,
                               (unsigned long long)kernel->end,
                               (unsigned long long)(kernel->end - kernel->start));
                        printf("  寄存器/线程: %u, 共享内存: %u bytes\n",
                               kernel->registersPerThread, kernel->sharedMemoryConfig);
                        break;
                    }
                    case CUPTI_ACTIVITY_KIND_MEMCPY: {
                        CUptiActivityMemcpy *memcpy = (CUptiActivityMemcpy *)record;
                        printf("内存拷贝:\n");
                        printf("  类型: %u, 大小: %llu bytes\n", 
                               memcpy->copyKind, (unsigned long long)memcpy->bytes);
                        printf("  时间: %llu - %llu ns (持续时间: %llu ns)\n",
                               (unsigned long long)memcpy->start,
                               (unsigned long long)memcpy->end,
                               (unsigned long long)(memcpy->end - memcpy->start));
                        printf("  带宽: %.2f GB/s\n", 
                               (double)memcpy->bytes / (memcpy->end - memcpy->start));
                        break;
                    }
                    case CUPTI_ACTIVITY_KIND_MEMSET: {
                        CUptiActivityMemset *memset = (CUptiActivityMemset *)record;
                        printf("内存设置:\n");
                        printf("  大小: %llu bytes, 值: 0x%x\n", 
                               (unsigned long long)memset->bytes, memset->value);
                        printf("  时间: %llu - %llu ns\n",
                               (unsigned long long)memset->start,
                               (unsigned long long)memset->end);
                        break;
                    }
                    default:
                        printf("其他Activity类型: %u\n", record->kind);
                        break;
                }
                printf("\n");
            } else if (status == CUPTI_ERROR_MAX_LIMIT_REACHED) {
                break;
            } else {
                CUPTI_CALL(status);
            }
        } while (1);
        
        // 重置buffer以便重用
        size_t dropped;
        CUPTI_CALL(cuptiActivityGetNumDroppedRecords(ctx, streamId, &dropped));
        if (dropped != 0) {
            printf("警告: 丢失了 %zu 条记录\n", dropped);
        }
    }
}

// 初始化CUPTI
void initCupti() {
    printf("初始化CUPTI...\n");
    
    // 分配activity buffer
    s_pActivityBuffer = (uint8_t *)malloc(BUF_SIZE + ALIGN_SIZE);
    if (s_pActivityBuffer == NULL) {
        printf("错误: 无法分配activity buffer\n");
        exit(-1);
    }
    
    s_pAlignedActivityBuffer = ALIGN_BUFFER(s_pActivityBuffer, ALIGN_SIZE);
    s_validBufferSize = BUF_SIZE;
    
    // 注册回调函数
    CUPTI_CALL(cuptiActivityRegisterCallbacks(bufferRequested, bufferCompleted));
    
    // 启用activity类型
    CUPTI_CALL(cuptiActivityEnable(CUPTI_ACTIVITY_KIND_KERNEL));
    CUPTI_CALL(cuptiActivityEnable(CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL));
    CUPTI_CALL(cuptiActivityEnable(CUPTI_ACTIVITY_KIND_MEMCPY));
    CUPTI_CALL(cuptiActivityEnable(CUPTI_ACTIVITY_KIND_MEMSET));
    
    printf("CUPTI初始化完成\n\n");
}

// 清理CUPTI
void finalizeCupti() {
    printf("\n清理CUPTI...\n");
    
    // 刷新所有activity记录
    CUPTI_CALL(cuptiActivityFlushAll(0));
    
    // 禁用activity
    CUPTI_CALL(cuptiActivityDisable(CUPTI_ACTIVITY_KIND_KERNEL));
    CUPTI_CALL(cuptiActivityDisable(CUPTI_ACTIVITY_KIND_CONCURRENT_KERNEL));
    CUPTI_CALL(cuptiActivityDisable(CUPTI_ACTIVITY_KIND_MEMCPY));
    CUPTI_CALL(cuptiActivityDisable(CUPTI_ACTIVITY_KIND_MEMSET));
    
    // 释放buffer
    free(s_pActivityBuffer);
    
    printf("CUPTI清理完成\n");
}

// 执行一些CUDA操作用于测试
void runCudaOperations() {
    printf("=== 执行CUDA操作 ===\n");
    
    const int n = 1024 * 1024;  // 1M elements
    const int size = n * sizeof(float);
    
    // 分配主机内存
    float *h_a = (float*)malloc(size);
    float *h_b = (float*)malloc(size);
    float *h_c = (float*)malloc(size);
    
    // 初始化数据
    for (int i = 0; i < n; i++) {
        h_a[i] = (float)i;
        h_b[i] = (float)(i * 2);
    }
    
    // 分配GPU内存
    float *d_a, *d_b, *d_c;
    cudaMalloc(&d_a, size);
    cudaMalloc(&d_b, size);
    cudaMalloc(&d_c, size);
    
    // 内存拷贝 H2D
    cudaMemcpy(d_a, h_a, size, cudaMemcpyHostToDevice);
    cudaMemcpy(d_b, h_b, size, cudaMemcpyHostToDevice);
    
    // 执行kernel
    int blockSize = 256;
    int gridSize = (n + blockSize - 1) / blockSize;
    
    printf("启动向量加法kernel: grid(%d), block(%d)\n", gridSize, blockSize);
    vectorAdd<<<gridSize, blockSize>>>(d_a, d_b, d_c, n);
    
    // 同步
    cudaDeviceSynchronize();
    
    // 内存拷贝 D2H
    cudaMemcpy(h_c, d_c, size, cudaMemcpyDeviceToHost);
    
    // 验证结果
    bool success = true;
    for (int i = 0; i < 10; i++) {  // 只检查前10个元素
        if (h_c[i] != h_a[i] + h_b[i]) {
            success = false;
            break;
        }
    }
    
    printf("计算结果: %s\n", success ? "正确" : "错误");
    printf("前几个结果: %.0f, %.0f, %.0f, %.0f, %.0f\n", 
           h_c[0], h_c[1], h_c[2], h_c[3], h_c[4]);
    
    // 清理
    cudaFree(d_a);
    cudaFree(d_b);
    cudaFree(d_c);
    free(h_a);
    free(h_b);
    free(h_c);
    
    printf("CUDA操作完成\n");
}

int main() {
    printf("=== CUPTI Activity API Demo ===\n\n");
    
    // 初始化CUDA
    cudaSetDevice(0);
    
    // 初始化CUPTI
    initCupti();
    
    // 执行CUDA操作
    runCudaOperations();
    
    // 等待一下让activity记录完成
    sleep(1);
    
    // 清理CUPTI
    finalizeCupti();
    
    printf("\nDemo完成\n");
    return 0;
}
